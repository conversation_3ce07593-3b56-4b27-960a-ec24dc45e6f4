{"functions": {"api/danmaku/get.mjs": {"maxDuration": 30}, "api/danmaku/save.mjs": {"maxDuration": 30}, "api/danmaku/delete.mjs": {"maxDuration": 30}}, "rewrites": [{"source": "/proxy/:path*", "destination": "/api/proxy/:path*"}, {"source": "/s=:query", "destination": "/index.html"}, {"source": "/player.html", "destination": "/player.html"}, {"source": "/player.html/:path*", "destination": "/player.html"}, {"source": "/:path*", "destination": "/:path*"}]}