// /api/danmaku/delete.mjs - 删除弹幕数据
import { Redis } from '@upstash/redis';

// Redis 配置
let redis = null;
if (process.env.KV_REST_API_URL && process.env.KV_REST_API_TOKEN) {
  redis = new Redis({
    url: process.env.KV_REST_API_URL,
    token: process.env.KV_REST_API_TOKEN,
  });
}

export default async function handler(req, res) {
  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'DELETE') {
    return res.status(405).json({ error: '方法不允许' });
  }

  try {
    const { id, danmakuId } = req.body;

    if (!id || !danmakuId) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const safeId = id.replace(/[^a-zA-Z0-9_-]/g, '_');

    if (!redis) {
      return res.status(503).json({ error: '存储服务不可用' });
    }

    // 读取现有数据
    let danmakuList = [];
    try {
      const data = await redis.get(`danmaku:${safeId}`);
      danmakuList = data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Redis 读取弹幕错误:', error);
      return res.status(404).json({ error: '弹幕数据不存在' });
    }

    const originalLength = danmakuList.length;
    danmakuList = danmakuList.filter(item => item.id !== danmakuId);

    if (danmakuList.length === originalLength) {
      return res.status(404).json({ error: '弹幕不存在' });
    }

    // 保存更新后的数据
    try {
      await redis.set(`danmaku:${safeId}`, JSON.stringify(danmakuList));
      console.log(`删除弹幕 (Redis): ${id}, ID: ${danmakuId}`);
      return res.json({ success: true });
    } catch (error) {
      console.error('Redis 保存弹幕错误:', error);
      return res.status(500).json({ error: '删除弹幕数据失败' });
    }
  } catch (error) {
    console.error('删除弹幕数据错误:', error);
    return res.status(500).json({ error: '删除弹幕数据失败' });
  }
}
