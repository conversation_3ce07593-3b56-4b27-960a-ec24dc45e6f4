// 简化版弹幕保存 API - 使用 GitHub Gist 作为存储
import crypto from 'crypto';

export default async function handler(req, res) {
  // 设置 CORS 头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: '方法不允许' });
  }

  try {
    const { id, danmaku } = req.body;
    console.log(`弹幕保存请求 - ID: ${id}`);

    if (!id || !danmaku) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    // 验证弹幕数据格式
    if (!danmaku.text || typeof danmaku.text !== 'string') {
      return res.status(400).json({ error: '弹幕文本无效' });
    }

    if (danmaku.text.length > 200) {
      return res.status(400).json({ error: '弹幕文本过长' });
    }

    // GitHub Gist 配置
    const GITHUB_TOKEN = process.env.GITHUB_TOKEN;
    const GIST_ID = process.env.GIST_ID;
    
    if (!GITHUB_TOKEN || !GIST_ID) {
      console.log('GitHub 配置缺失，无法保存到云端');
      return res.status(503).json({ 
        error: '存储服务不可用',
        details: 'GitHub 配置未设置'
      });
    }

    const safeId = id.replace(/[^a-zA-Z0-9_-]/g, '_');
    const filename = `danmaku_${safeId}.json`;

    // 读取现有数据
    let danmakuList = [];
    try {
      const response = await fetch(`https://api.github.com/gists/${GIST_ID}`, {
        headers: {
          'Authorization': `token ${GITHUB_TOKEN}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });

      if (response.ok) {
        const gist = await response.json();
        const file = gist.files[filename];
        
        if (file && file.content) {
          danmakuList = JSON.parse(file.content);
        }
      }
    } catch (error) {
      console.error('读取现有弹幕数据失败:', error);
      danmakuList = [];
    }

    // 添加新弹幕
    const newDanmaku = {
      text: danmaku.text.trim(),
      time: parseFloat(danmaku.time) || 0,
      mode: parseInt(danmaku.mode) || 0,
      color: danmaku.color || '#FFFFFF',
      timestamp: Date.now(),
      id: crypto.randomUUID()
    };

    danmakuList.push(newDanmaku);

    // 保存到 GitHub Gist
    try {
      const updateResponse = await fetch(`https://api.github.com/gists/${GIST_ID}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `token ${GITHUB_TOKEN}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          files: {
            [filename]: {
              content: JSON.stringify(danmakuList, null, 2)
            }
          }
        })
      });

      if (updateResponse.ok) {
        console.log(`保存弹幕到 Gist: ${id}, 内容: ${newDanmaku.text}`);
        return res.json({ success: true, danmaku: newDanmaku });
      } else {
        console.error('保存到 Gist 失败:', updateResponse.status);
        return res.status(500).json({ error: '保存弹幕数据失败' });
      }
    } catch (error) {
      console.error('保存弹幕到 Gist 错误:', error);
      return res.status(500).json({ error: '保存弹幕数据失败' });
    }
  } catch (error) {
    console.error('保存弹幕数据错误:', error);
    return res.status(500).json({ error: '保存弹幕数据失败', details: error.message });
  }
}
